<?php
header('Content-Type: application/json');

function scanFilesBySemester($rootDir)
{
    $result = [];

    $semesters = scandir($rootDir);
    foreach ($semesters as $semester) {
        if ($semester === '.' || $semester === '..')
            continue;

        $semesterPath = $rootDir . DIRECTORY_SEPARATOR . $semester;
        if (!is_dir($semesterPath))
            continue;

        $result[$semester] = [];

        $subjects = scandir($semesterPath);
        foreach ($subjects as $subject) {
            if ($subject === '.' || $subject === '..')
                continue;

            $subjectPath = $semesterPath . DIRECTORY_SEPARATOR . $subject;
            if (!is_dir($subjectPath))
                continue;

            $files = scandir($subjectPath);
            foreach ($files as $file) {
                if ($file === '.' || $file === '..')
                    continue;

                if (strtolower(pathinfo($file, PATHINFO_EXTENSION)) === 'docx') {
                    $result[$semester][$subject][] = $file;
                }
            }
        }
    }

    return $result;
}

// echo json_encode(scanFilesBySemester('./files'), JSON_PRETTY_PRINT);
